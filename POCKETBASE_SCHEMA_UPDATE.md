# 🔄 PocketBase Schema Update - Production Ready

## 📋 <PERSON><PERSON><PERSON>

### **<PERSON><PERSON><PERSON> vs <PERSON><PERSON>**

#### **Collection: users**
```diff
- Simple auth collection
+ Production schema dengan fields tambahan:
  + telepon (number)
  + alamat (text)
  + idUnique (text)
```

#### **Collection: sensor_data**
```diff
- Flat structure dengan individual sensor fields
+ Normalized structure:
  + deviceId (relation to devices)
  + sensorType (text) - "temperature", "humidity", etc.
  + value (number) - sensor reading value
  + unit (text) - "celsius", "percent", etc.
  + timestamp (date) - when reading was taken
```

#### **Collection: devices**
```diff
- deviceName, deviceId, location, isActive, userId
+ Production schema:
  + idAlat (number) - device hardware ID
  + namaAlat (text) - device name
  + userId (relation to users)
  + status (number) - 0/1 for inactive/active
```

#### **Collection: controls**
```diff
- deviceId (text)
+ deviceId (relation to devices) - proper foreign key
  + idKontrol (number) - control hardware ID
  + namaKontrol (text) - control name
  + isON (bool) - current state
  + automated (bool) - automation enabled
  + parameter (text) - control parameters
```

## 🛠️ File yang Diperbaiki

### **1. `pocketbase_scheduling_only.json`** ⭐
- ✅ Ditambahkan production schema collections
- ✅ Proper relations antar collections
- ✅ Scheduling collections (schedules, automation_rules, schedule_executions)
- ✅ Ready untuk import ke PocketBase

### **2. `lib/api/schedule_api.dart`**
- ✅ Updated `_getDeviceId()` method menjadi async
- ✅ Auto-fetch deviceId dari devices collection
- ✅ Fallback ke sample device ID jika tidak ada
- ✅ Proper error handling

### **3. `lib/widgets/schedule_modal.dart`**
- ✅ Import SharedPreferences
- ✅ Updated `_saveSchedule()` untuk menggunakan deviceId yang benar
- ✅ Fallback ke production device ID

## 📊 Schema Relations

```mermaid
graph TD
    A[users] --> B[devices]
    B --> C[sensor_data]
    B --> D[controls]
    B --> E[schedules]
    B --> F[automation_rules]
    E --> G[schedule_executions]
```

### **Relation Details:**
- `devices.userId` → `users.id`
- `sensor_data.deviceId` → `devices.id`
- `controls.deviceId` → `devices.id`
- `schedules.deviceId` → `devices.id` (text reference)
- `automation_rules.deviceId` → `devices.id` (text reference)
- `schedule_executions.scheduleId` → `schedules.id`

## 🚀 Cara Import Schema

### **Step 1: Backup Data Existing**
```bash
# Backup PocketBase database
cp pb_data/data.db pb_data/data_backup_$(date +%Y%m%d).db
```

### **Step 2: Import Collections**
1. Buka PocketBase Admin: `http://34.101.210.210:8080/_/`
2. Login sebagai admin
3. **Settings** → **Import collections**
4. Copy-paste isi file `pocketbase_scheduling_only.json`
5. Klik **Import**

### **Step 3: Verify Import**
Check collections yang terbuat:
- ✅ `users` (auth collection)
- ✅ `sensor_data` (normalized sensor readings)
- ✅ `devices` (device management)
- ✅ `controls` (device controls)
- ✅ `schedules` (scheduling system)
- ✅ `automation_rules` (automation logic)
- ✅ `schedule_executions` (execution logs)

## 📱 Flutter App Changes

### **API Integration**
```dart
// Old way
String deviceId = 'device_123'; // hardcoded

// New way
String? deviceId = await _getDeviceId(); // dynamic from API
```

### **Schedule Creation**
```dart
// Old way
deviceId: 'device_123', // hardcoded

// New way
SharedPreferences pref = await SharedPreferences.getInstance();
String? deviceId = pref.getString('deviceId') ?? '016j4islabono91';
```

## 🔧 Sample Data Structure

### **Users**
```json
{
  "username": "farmer1",
  "email": "<EMAIL>",
  "name": "Budi Santoso",
  "telepon": 628123456789,
  "alamat": "Jl. Pertanian No. 123",
  "idUnique": "FARM001"
}
```

### **Devices**
```json
{
  "idAlat": 123,
  "namaAlat": "Smart Farm Greenhouse A",
  "userId": "user_id_here",
  "status": 1
}
```

### **Sensor Data**
```json
{
  "deviceId": "device_id_here",
  "sensorType": "temperature",
  "value": 28.5,
  "unit": "celsius",
  "timestamp": "2024-01-15T06:00:00.000Z"
}
```

### **Controls**
```json
{
  "deviceId": "device_id_here",
  "idKontrol": 1,
  "namaKontrol": "Sprinkler System",
  "isON": false,
  "automated": true,
  "parameter": "Auto mode - Soil moisture < 40%"
}
```

### **Schedules**
```json
{
  "name": "Penyiraman Pagi",
  "description": "Penyiraman otomatis setiap pagi",
  "deviceId": "device_id_here",
  "controlId": "sprinkler",
  "type": "time_based",
  "startTime": "2024-01-15T06:00:00.000Z",
  "endTime": "2024-01-15T06:30:00.000Z",
  "daysOfWeek": [1, 2, 3, 4, 5],
  "isActive": true,
  "isRepeating": true,
  "parameters": {
    "duration": 30,
    "intensity": "medium"
  }
}
```

## ✅ Testing Checklist

### **PocketBase Collections**
- [ ] Import berhasil tanpa error
- [ ] Semua 7 collections terbuat
- [ ] Relations berfungsi dengan benar
- [ ] API rules sudah diset

### **Flutter App**
- [ ] Login masih berfungsi
- [ ] Sensor dashboard menampilkan data
- [ ] Control devices berfungsi
- [ ] Schedule modal terbuka
- [ ] Schedule creation berhasil
- [ ] Schedule list menampilkan data

### **API Endpoints**
- [ ] `GET /api/collections/users/records`
- [ ] `GET /api/collections/devices/records`
- [ ] `GET /api/collections/sensor_data/records`
- [ ] `GET /api/collections/controls/records`
- [ ] `GET /api/collections/schedules/records`
- [ ] `POST /api/collections/schedules/records`

## 🚨 Troubleshooting

### **Error: "Collection already exists"**
- Hapus collections yang conflict
- Import ulang dengan schema baru

### **Error: "Field type cannot be changed"**
- Backup data existing
- Drop collections yang conflict
- Import schema baru
- Migrate data manually jika diperlukan

### **Error: "Relation not found"**
- Pastikan target collection sudah ada
- Check collection IDs match dengan schema
- Verify relation fields sudah benar

### **App Error: "deviceId not found"**
- Check SharedPreferences untuk deviceId
- Verify user sudah login
- Check devices collection ada data
- Fallback ke sample device ID

## 🎯 Next Steps

1. **Import schema** menggunakan file JSON
2. **Test API endpoints** dengan Postman/curl
3. **Test Flutter app** dengan schema baru
4. **Migrate existing data** jika ada
5. **Add sample data** untuk testing
6. **Implement background scheduler**
7. **Add push notifications**

---

**📞 Support**: Jika ada masalah, pastikan:
- PocketBase server running
- Collections sudah diimport
- API rules sudah diset
- Flutter app sudah rebuild
