[{"id": "_pb_users_auth_", "name": "users", "type": "auth", "system": false, "schema": [{"system": false, "id": "users_name", "name": "name", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "users_avatar", "name": "avatar", "type": "file", "required": false, "presentable": false, "unique": false, "options": {"mimeTypes": ["image/jpeg", "image/png", "image/svg+xml", "image/gif", "image/webp"], "thumbs": null, "maxSelect": 1, "maxSize": 5242880, "protected": false}}, {"system": false, "id": "yv9uznlj", "name": "telepon", "type": "number", "required": false, "presentable": false, "unique": false, "options": {"min": null, "max": null, "noDecimal": false}}, {"system": false, "id": "jxooedml", "name": "<PERSON><PERSON><PERSON>", "type": "text", "required": false, "presentable": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "84snbg4c", "name": "idUnique", "type": "text", "required": false, "presentable": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}], "indexes": [], "listRule": "id = @request.auth.id", "viewRule": "id = @request.auth.id", "createRule": "", "updateRule": "id = @request.auth.id", "deleteRule": "id = @request.auth.id", "options": {"allowEmailAuth": true, "allowOAuth2Auth": true, "allowUsernameAuth": true, "exceptEmailDomains": null, "manageRule": null, "minPasswordLength": 8, "onlyEmailDomains": null, "onlyVerified": false, "requireEmail": false}}, {"id": "bxbt56uwby2fjbv", "name": "sensor_data", "type": "base", "system": false, "schema": [{"system": false, "id": "2a5ta1ut", "name": "deviceId", "type": "relation", "required": true, "presentable": true, "unique": false, "options": {"collectionId": "016j4islabono91", "cascadeDelete": true, "minSelect": null, "maxSelect": 1, "displayFields": null}}, {"system": false, "id": "p8vqcgvw", "name": "sensorType", "type": "text", "required": true, "presentable": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "l2pddkwo", "name": "value", "type": "number", "required": true, "presentable": true, "unique": false, "options": {"min": null, "max": null, "noDecimal": false}}, {"system": false, "id": "t89jrqpj", "name": "unit", "type": "text", "required": false, "presentable": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "gv8ckcpa", "name": "timestamp", "type": "date", "required": true, "presentable": true, "unique": false, "options": {"min": "", "max": ""}}], "indexes": [], "listRule": null, "viewRule": null, "createRule": null, "updateRule": null, "deleteRule": null, "options": {}}, {"id": "016j4islabono91", "name": "devices", "type": "base", "system": false, "schema": [{"system": false, "id": "vjzdceu0", "name": "idAlat", "type": "number", "required": true, "presentable": true, "unique": false, "options": {"min": null, "max": null, "noDecimal": false}}, {"system": false, "id": "zqtvlmg7", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "text", "required": true, "presentable": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "i2rntlpo", "name": "userId", "type": "relation", "required": true, "presentable": true, "unique": false, "options": {"collectionId": "_pb_users_auth_", "cascadeDelete": true, "minSelect": null, "maxSelect": 1, "displayFields": null}}, {"system": false, "id": "y6qqn74b", "name": "status", "type": "number", "required": true, "presentable": true, "unique": false, "options": {"min": 0, "max": 1, "noDecimal": false}}], "indexes": [], "listRule": null, "viewRule": null, "createRule": null, "updateRule": null, "deleteRule": null, "options": {}}, {"id": "wbm9k5w09xvt6pf", "name": "controls", "type": "base", "system": false, "schema": [{"system": false, "id": "5ebyyyyf", "name": "deviceId", "type": "relation", "required": true, "presentable": true, "unique": false, "options": {"collectionId": "016j4islabono91", "cascadeDelete": true, "minSelect": null, "maxSelect": 1, "displayFields": null}}, {"system": false, "id": "10mpb3ps", "name": "idKontrol", "type": "number", "required": true, "presentable": true, "unique": false, "options": {"min": 1, "max": null, "noDecimal": false}}, {"system": false, "id": "yqwwibez", "name": "namaKontrol", "type": "text", "required": true, "presentable": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"system": false, "id": "recvm3cc", "name": "isON", "type": "bool", "required": false, "presentable": true, "unique": false, "options": {}}, {"system": false, "id": "hztaiioz", "name": "automated", "type": "bool", "required": false, "presentable": true, "unique": false, "options": {}}, {"system": false, "id": "jdmqgtwz", "name": "parameter", "type": "text", "required": false, "presentable": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}], "indexes": [], "listRule": null, "viewRule": null, "createRule": null, "updateRule": null, "deleteRule": null, "options": {}}, {"id": "pf8k2x9m1schedules", "name": "schedules", "type": "base", "system": false, "schema": [{"system": false, "id": "schedule_name_field", "name": "name", "type": "text", "required": true, "presentable": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"system": false, "id": "schedule_description", "name": "description", "type": "text", "required": false, "presentable": false, "unique": false, "options": {"min": null, "max": 1000, "pattern": ""}}, {"system": false, "id": "schedule_device_id", "name": "deviceId", "type": "text", "required": true, "presentable": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"system": false, "id": "schedule_control_id", "name": "controlId", "type": "text", "required": true, "presentable": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"system": false, "id": "schedule_type_field", "name": "type", "type": "select", "required": true, "presentable": true, "unique": false, "options": {"maxSelect": 1, "values": ["time_based", "sensor_based", "manual"]}}, {"system": false, "id": "schedule_start_time", "name": "startTime", "type": "date", "required": true, "presentable": true, "unique": false, "options": {"min": "", "max": ""}}, {"system": false, "id": "schedule_end_time", "name": "endTime", "type": "date", "required": false, "presentable": false, "unique": false, "options": {"min": "", "max": ""}}, {"system": false, "id": "schedule_days_week", "name": "daysOfWeek", "type": "json", "required": false, "presentable": false, "unique": false, "options": {"maxSize": 1000}}, {"system": false, "id": "schedule_is_active", "name": "isActive", "type": "bool", "required": false, "presentable": true, "unique": false, "options": {}}, {"system": false, "id": "schedule_repeating", "name": "isRepeating", "type": "bool", "required": false, "presentable": false, "unique": false, "options": {}}, {"system": false, "id": "schedule_parameters", "name": "parameters", "type": "json", "required": false, "presentable": false, "unique": false, "options": {"maxSize": 5000}}], "indexes": ["CREATE INDEX idx_schedules_device ON schedules (deviceId)", "CREATE INDEX idx_schedules_active ON schedules (isActive)", "CREATE INDEX idx_schedules_type ON schedules (type)"], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "options": {}}, {"id": "automation_rules_col", "name": "automation_rules", "type": "base", "system": false, "schema": [{"system": false, "id": "automation_name_field", "name": "name", "type": "text", "required": true, "presentable": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"system": false, "id": "automation_description", "name": "description", "type": "text", "required": false, "presentable": false, "unique": false, "options": {"min": null, "max": 1000, "pattern": ""}}, {"system": false, "id": "automation_device_id", "name": "deviceId", "type": "text", "required": true, "presentable": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"system": false, "id": "automation_control_id", "name": "controlId", "type": "text", "required": true, "presentable": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"system": false, "id": "automation_conditions", "name": "conditions", "type": "json", "required": true, "presentable": false, "unique": false, "options": {"maxSize": 10000}}, {"system": false, "id": "automation_actions", "name": "actions", "type": "json", "required": true, "presentable": false, "unique": false, "options": {"maxSize": 10000}}, {"system": false, "id": "automation_is_active", "name": "isActive", "type": "bool", "required": false, "presentable": true, "unique": false, "options": {}}, {"system": false, "id": "automation_logic", "name": "logic", "type": "select", "required": true, "presentable": true, "unique": false, "options": {"maxSelect": 1, "values": ["AND", "OR"]}}], "indexes": ["CREATE INDEX idx_automation_device ON automation_rules (deviceId)", "CREATE INDEX idx_automation_active ON automation_rules (isActive)"], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "options": {}}, {"id": "schedule_executions_col", "name": "schedule_executions", "type": "base", "system": false, "schema": [{"system": false, "id": "execution_schedule_id", "name": "scheduleId", "type": "text", "required": true, "presentable": true, "unique": false, "options": {"min": 1, "max": 255, "pattern": ""}}, {"system": false, "id": "execution_executed_at", "name": "executedAt", "type": "date", "required": true, "presentable": true, "unique": false, "options": {"min": "", "max": ""}}, {"system": false, "id": "execution_status", "name": "status", "type": "select", "required": true, "presentable": true, "unique": false, "options": {"maxSelect": 1, "values": ["success", "failed", "skipped"]}}, {"system": false, "id": "execution_result", "name": "result", "type": "text", "required": false, "presentable": false, "unique": false, "options": {"min": null, "max": 2000, "pattern": ""}}, {"system": false, "id": "execution_error", "name": "error", "type": "text", "required": false, "presentable": false, "unique": false, "options": {"min": null, "max": 2000, "pattern": ""}}], "indexes": ["CREATE INDEX idx_executions_schedule ON schedule_executions (scheduleId)", "CREATE INDEX idx_executions_status ON schedule_executions (status)", "CREATE INDEX idx_executions_date ON schedule_executions (executedAt)"], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "options": {}}]