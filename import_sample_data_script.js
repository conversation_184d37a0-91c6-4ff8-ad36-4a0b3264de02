// PocketBase Sample Data Import Script
// Run this in browser console after collections are created
// Make sure you're logged in as admin first

const POCKETBASE_URL = 'http://**************:8080';

// Sample Automation Rules Data
const automationRulesData = [
  {
    "name": "Auto Sprinkler - Tanah <PERSON>",
    "description": "Nyalakan sprinkler otomatis jika kelembaban tanah di bawah 35%",
    "deviceId": "016j4islabono91",
    "controlId": "sprinkler",
    "conditions": [
      {
        "sensor": "soilMoisture",
        "operator": "<",
        "value": 35,
        "unit": "percent"
      }
    ],
    "actions": [
      {
        "type": "turn_on",
        "controlId": "sprinkler",
        "parameters": {
          "duration": 20,
          "intensity": "medium",
          "flow_rate": "5L/min"
        }
      }
    ],
    "isActive": true,
    "logic": "AND"
  },
  {
    "name": "Auto Kipas - Suhu <PERSON>",
    "description": "Nyalakan kipas angin jika suhu lebih dari 32°C",
    "deviceId": "016j4islabono91",
    "controlId": "kipas angin",
    "conditions": [
      {
        "sensor": "temperature",
        "operator": ">",
        "value": 32,
        "unit": "celsius"
      }
    ],
    "actions": [
      {
        "type": "turn_on",
        "controlId": "kipas angin",
        "parameters": {
          "speed": "high",
          "duration": 60
        }
      }
    ],
    "isActive": true,
    "logic": "AND"
  },
  {
    "name": "Auto Mist - Kelembaban Rendah",
    "description": "Nyalakan mist system jika kelembaban udara di bawah 55% dan suhu tinggi",
    "deviceId": "016j4islabono91",
    "controlId": "mist",
    "conditions": [
      {
        "sensor": "humidity",
        "operator": "<",
        "value": 55,
        "unit": "percent"
      },
      {
        "sensor": "temperature",
        "operator": ">",
        "value": 28,
        "unit": "celsius"
      }
    ],
    "actions": [
      {
        "type": "turn_on",
        "controlId": "mist",
        "parameters": {
          "duration": 10,
          "intensity": "fine",
          "humidity_target": 70
        }
      }
    ],
    "isActive": true,
    "logic": "AND"
  }
];

// Sample Sensor Data
const sensorData = [
  {
    "deviceId": "016j4islabono91",
    "sensorType": "temperature",
    "value": 28.5,
    "unit": "celsius",
    "timestamp": "2024-01-15T06:00:00.000Z"
  },
  {
    "deviceId": "016j4islabono91",
    "sensorType": "humidity",
    "value": 65.2,
    "unit": "percent",
    "timestamp": "2024-01-15T06:00:00.000Z"
  },
  {
    "deviceId": "016j4islabono91",
    "sensorType": "soilMoisture",
    "value": 45.8,
    "unit": "percent",
    "timestamp": "2024-01-15T06:00:00.000Z"
  },
  {
    "deviceId": "016j4islabono91",
    "sensorType": "lightIntensity",
    "value": 850.0,
    "unit": "lux",
    "timestamp": "2024-01-15T06:00:00.000Z"
  },
  {
    "deviceId": "016j4islabono91",
    "sensorType": "ph",
    "value": 6.8,
    "unit": "ph",
    "timestamp": "2024-01-15T06:00:00.000Z"
  },
  {
    "deviceId": "016j4islabono91",
    "sensorType": "temperature",
    "value": 32.8,
    "unit": "celsius",
    "timestamp": "2024-01-15T12:00:00.000Z"
  },
  {
    "deviceId": "016j4islabono91",
    "sensorType": "humidity",
    "value": 58.3,
    "unit": "percent",
    "timestamp": "2024-01-15T12:00:00.000Z"
  },
  {
    "deviceId": "016j4islabono91",
    "sensorType": "soilMoisture",
    "value": 35.2,
    "unit": "percent",
    "timestamp": "2024-01-15T12:00:00.000Z"
  }
];

// Sample Schedule Executions Data
const scheduleExecutionsData = [
  {
    "scheduleId": "pf8k2x9m1schedules_001",
    "executedAt": "2024-01-15T06:00:00.000Z",
    "status": "success",
    "result": "Sprinkler activated successfully for 30 minutes. Soil moisture increased from 38% to 52%",
    "error": ""
  },
  {
    "scheduleId": "pf8k2x9m1schedules_002",
    "executedAt": "2024-01-15T17:00:00.000Z",
    "status": "success",
    "result": "Drip irrigation completed successfully. Duration: 45 minutes. Water usage: 90L",
    "error": ""
  },
  {
    "scheduleId": "pf8k2x9m1schedules_001",
    "executedAt": "2024-01-14T06:00:00.000Z",
    "status": "failed",
    "result": "",
    "error": "Water pump malfunction detected. Unable to start sprinkler system"
  }
];

// Function to get auth token from localStorage or prompt user
function getAuthToken() {
  // Try to get from localStorage first
  const authData = localStorage.getItem('pocketbase_auth');
  if (authData) {
    try {
      const parsed = JSON.parse(authData);
      return parsed.token;
    } catch (e) {
      console.log('Could not parse auth data from localStorage');
    }
  }
  
  // If not found, prompt user
  const token = prompt('Please enter your admin auth token (or login first in another tab):');
  return token;
}

// Function to import automation rules
async function importAutomationRules() {
  console.log('🤖 Importing automation rules...');
  
  const token = getAuthToken();
  if (!token) {
    console.error('❌ No auth token provided');
    return;
  }

  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  };

  let successCount = 0;
  let errorCount = 0;

  for (const rule of automationRulesData) {
    try {
      const response = await fetch(`${POCKETBASE_URL}/api/collections/automation_rules/records`, {
        method: 'POST',
        headers,
        body: JSON.stringify(rule)
      });

      if (response.ok) {
        const result = await response.json();
        console.log(`✅ Automation rule created: ${rule.name}`);
        successCount++;
      } else {
        const error = await response.text();
        console.error(`❌ Failed to create rule "${rule.name}": ${error}`);
        errorCount++;
      }
    } catch (error) {
      console.error(`❌ Error creating rule "${rule.name}":`, error);
      errorCount++;
    }
  }

  console.log(`🎉 Automation rules import completed: ${successCount} success, ${errorCount} errors`);
}

// Function to import sensor data
async function importSensorData() {
  console.log('📊 Importing sensor data...');
  
  const token = getAuthToken();
  if (!token) {
    console.error('❌ No auth token provided');
    return;
  }

  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  };

  let successCount = 0;
  let errorCount = 0;

  for (const data of sensorData) {
    try {
      const response = await fetch(`${POCKETBASE_URL}/api/collections/sensor_data/records`, {
        method: 'POST',
        headers,
        body: JSON.stringify(data)
      });

      if (response.ok) {
        const result = await response.json();
        console.log(`✅ Sensor data created: ${data.sensorType} = ${data.value}${data.unit}`);
        successCount++;
      } else {
        const error = await response.text();
        console.error(`❌ Failed to create sensor data: ${error}`);
        errorCount++;
      }
    } catch (error) {
      console.error(`❌ Error creating sensor data:`, error);
      errorCount++;
    }
  }

  console.log(`🎉 Sensor data import completed: ${successCount} success, ${errorCount} errors`);
}

// Function to import schedule executions
async function importScheduleExecutions() {
  console.log('📋 Importing schedule executions...');
  
  const token = getAuthToken();
  if (!token) {
    console.error('❌ No auth token provided');
    return;
  }

  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  };

  let successCount = 0;
  let errorCount = 0;

  for (const execution of scheduleExecutionsData) {
    try {
      const response = await fetch(`${POCKETBASE_URL}/api/collections/schedule_executions/records`, {
        method: 'POST',
        headers,
        body: JSON.stringify(execution)
      });

      if (response.ok) {
        const result = await response.json();
        console.log(`✅ Schedule execution created: ${execution.status} - ${execution.scheduleId}`);
        successCount++;
      } else {
        const error = await response.text();
        console.error(`❌ Failed to create execution: ${error}`);
        errorCount++;
      }
    } catch (error) {
      console.error(`❌ Error creating execution:`, error);
      errorCount++;
    }
  }

  console.log(`🎉 Schedule executions import completed: ${successCount} success, ${errorCount} errors`);
}

// Main function to import all sample data
async function importAllSampleData() {
  console.log('🚀 Starting sample data import...');
  console.log('Make sure you have:');
  console.log('1. ✅ Collections already created (schedules, automation_rules, schedule_executions, sensor_data)');
  console.log('2. ✅ Logged in as admin');
  console.log('3. ✅ PocketBase server running');
  console.log('');

  try {
    await importAutomationRules();
    await importSensorData();
    await importScheduleExecutions();
    
    console.log('');
    console.log('🎉 All sample data import completed!');
    console.log('📱 You can now test the Flutter app with sample data');
  } catch (error) {
    console.error('❌ Import failed:', error);
  }
}

// Instructions for usage
console.log(`
🔧 CARA MENGGUNAKAN SCRIPT INI:

1. Pastikan collections sudah dibuat terlebih dahulu:
   - schedules
   - automation_rules  
   - schedule_executions
   - sensor_data

2. Login ke PocketBase Admin: ${POCKETBASE_URL}/_/

3. Buka Developer Tools (F12) dan pergi ke Console tab

4. Copy-paste script ini ke console

5. Jalankan salah satu command:
   - importAllSampleData()           // Import semua data
   - importAutomationRules()         // Import automation rules saja
   - importSensorData()              // Import sensor data saja  
   - importScheduleExecutions()      // Import schedule executions saja

⚠️ PENTING: 
- Pastikan sudah login sebagai admin
- Collections harus sudah dibuat terlebih dahulu
- Script akan otomatis menggunakan auth token dari localStorage
`);

// Export functions for manual use
window.importAllSampleData = importAllSampleData;
window.importAutomationRules = importAutomationRules;
window.importSensorData = importSensorData;
window.importScheduleExecutions = importScheduleExecutions;
