{"collections": [], "data": {"automation_rules": [{"name": "Auto Sprinkler - <PERSON><PERSON>", "description": "Nyalakan sprinkler otomatis jika kelembaban tanah di bawah 35%", "deviceId": "016j4islabono91", "controlId": "sprinkler", "conditions": [{"sensor": "soilMoisture", "operator": "<", "value": 35, "unit": "percent"}], "actions": [{"type": "turn_on", "controlId": "sprinkler", "parameters": {"duration": 20, "intensity": "medium", "flow_rate": "5L/min"}}], "isActive": true, "logic": "AND"}, {"name": "Auto Kipas - <PERSON><PERSON>", "description": "<PERSON>yal<PERSON><PERSON> kipas angin jika suhu lebih dari 32°C", "deviceId": "016j4islabono91", "controlId": "kipas angin", "conditions": [{"sensor": "temperature", "operator": ">", "value": 32, "unit": "celsius"}], "actions": [{"type": "turn_on", "controlId": "kipas angin", "parameters": {"speed": "high", "duration": 60}}], "isActive": true, "logic": "AND"}, {"name": "Auto Mist - Kelembaban Rendah", "description": "Nyalakan mist system jika kelembaban udara di bawah 55% dan suhu tinggi", "deviceId": "016j4islabono91", "controlId": "mist", "conditions": [{"sensor": "humidity", "operator": "<", "value": 55, "unit": "percent"}, {"sensor": "temperature", "operator": ">", "value": 28, "unit": "celsius"}], "actions": [{"type": "turn_on", "controlId": "mist", "parameters": {"duration": 10, "intensity": "fine", "humidity_target": 70}}], "isActive": true, "logic": "AND"}, {"name": "Emergency Cooling", "description": "Sistem pendinginan darurat jika suhu sangat tinggi (>38°C)", "deviceId": "016j4islabono91", "controlId": "kipas angin", "conditions": [{"sensor": "temperature", "operator": ">", "value": 38, "unit": "celsius"}], "actions": [{"type": "turn_on", "controlId": "kipas angin", "parameters": {"speed": "maximum", "duration": 120}}, {"type": "turn_on", "controlId": "mist", "parameters": {"duration": 30, "intensity": "heavy"}}], "isActive": true, "logic": "AND"}, {"name": "Night Mode - Auto Off", "description": "<PERSON><PERSON>n semua peralatan pada malam hari kecuali yang essential", "deviceId": "016j4islabono91", "controlId": "all_devices", "conditions": [{"sensor": "lightIntensity", "operator": "<", "value": 100, "unit": "lux"}], "actions": [{"type": "turn_off", "controlId": "kipas angin", "parameters": {}}, {"type": "turn_off", "controlId": "mist", "parameters": {}}], "isActive": false, "logic": "AND"}, {"name": "pH Balance Control", "description": "Aktifkan sistem pH jika pH tanah tidak dalam range optimal", "deviceId": "016j4islabono91", "controlId": "ph_controller", "conditions": [{"sensor": "ph", "operator": "<", "value": 6.0, "unit": "ph"}], "actions": [{"type": "turn_on", "controlId": "ph_controller", "parameters": {"target_ph": 6.5, "duration": 15}}], "isActive": false, "logic": "OR"}]}}