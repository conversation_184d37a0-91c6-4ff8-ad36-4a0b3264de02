# 📊 Panduan Import Sample Data ke PocketBase

## 🎯 **File Sample Data yang Tersedia:**

1. **`import_automation_rules_data.json`** - Sample automation rules (3 rules)
2. **`import_sensor_data.json`** - Sample sensor readings (15 data points)
3. **`import_schedule_executions_data.json`** - Sample execution logs (10 executions)
4. **`import_schedules_data.json`** - Sample schedules (5 schedules)

## 🚀 **Cara Import Sample Data:**

### **Prerequisites:**
✅ Collections sudah dibuat terlebih dahulu:
- `schedules`
- `automation_rules`
- `schedule_executions`
- `sensor_data`

### **Step-by-Step Import:**

#### **Step 1: Import Schedules Data**
1. Buka PocketBase Admin: `http://**************:8080/_/`
2. Login sebagai admin
3. Klik **Settings** → **Import collections**
4. Copy-paste isi file `import_schedules_data.json`
5. Klik **Review** → **Import**
6. ✅ **Result**: 5 sample schedules terbuat

#### **Step 2: Import Automation Rules Data**
1. Masih di **Settings** → **Import collections**
2. Copy-paste isi file `import_automation_rules_data.json`
3. Klik **Review** → **Import**
4. ✅ **Result**: 3 automation rules terbuat

#### **Step 3: Import Sensor Data**
1. Masih di **Settings** → **Import collections**
2. Copy-paste isi file `import_sensor_data.json`
3. Klik **Review** → **Import**
4. ✅ **Result**: 15 sensor readings terbuat

#### **Step 4: Import Schedule Executions**
1. Masih di **Settings** → **Import collections**
2. Copy-paste isi file `import_schedule_executions_data.json`
3. Klik **Review** → **Import**
4. ✅ **Result**: 10 execution logs terbuat

## 📋 **Sample Data Overview:**

### **Schedules (5 items):**
- ✅ **Penyiraman Pagi** - Daily morning watering (weekdays)
- ✅ **Penyiraman Sore** - Daily evening watering (all days)
- ✅ **Ventilasi Siang** - Midday ventilation (all days)
- ✅ **Misting Pagi** - Morning misting (Mon, Wed, Fri)
- ✅ **Emergency Watering** - Manual emergency watering

### **Automation Rules (3 items):**
- ✅ **Auto Sprinkler** - Activate when soil moisture < 35%
- ✅ **Auto Kipas** - Activate when temperature > 32°C
- ✅ **Auto Mist** - Activate when humidity < 55% AND temp > 28°C

### **Sensor Data (15 items):**
- ✅ **Temperature** readings (28.5°C - 35.5°C)
- ✅ **Humidity** readings (52.1% - 68.9%)
- ✅ **Soil Moisture** readings (31.8% - 48.5%)
- ✅ **Light Intensity** readings (450 - 3200 lux)
- ✅ **pH** readings (6.5 - 7.0)

### **Schedule Executions (10 items):**
- ✅ **Success** executions (7 items)
- ✅ **Failed** executions (2 items)
- ✅ **Skipped** executions (1 item)

## ✅ **Verification Checklist:**

Setelah import selesai, verify di PocketBase Admin:

### **Check Collections Data:**
- [ ] **Schedules**: 5 records
- [ ] **Automation Rules**: 3 records
- [ ] **Sensor Data**: 15 records
- [ ] **Schedule Executions**: 10 records

### **Test API Endpoints:**
```bash
# Test schedules
curl "http://**************:8080/api/collections/schedules/records"

# Test automation rules
curl "http://**************:8080/api/collections/automation_rules/records"

# Test sensor data
curl "http://**************:8080/api/collections/sensor_data/records"

# Test schedule executions
curl "http://**************:8080/api/collections/schedule_executions/records"
```

### **Test Flutter App:**
- [ ] **Scheduling Screen** menampilkan schedules
- [ ] **Schedule Modal** bisa create new schedule
- [ ] **Sensor Dashboard** menampilkan sensor data
- [ ] **API calls** berfungsi tanpa error

## 🔧 **Sample Data Details:**

### **Realistic Sensor Values:**
```
Temperature: 28.5°C - 35.5°C (realistic greenhouse range)
Humidity: 52.1% - 68.9% (optimal plant growth range)
Soil Moisture: 31.8% - 48.5% (dry to optimal range)
Light Intensity: 450 - 3200 lux (dawn to midday)
pH: 6.5 - 7.0 (optimal soil pH range)
```

### **Practical Schedules:**
```
Morning Watering: 06:00-06:30 (weekdays)
Evening Watering: 17:00-17:45 (daily)
Midday Ventilation: 11:00-15:00 (daily)
Morning Misting: 07:00-07:15 (Mon/Wed/Fri)
Emergency Manual: On-demand
```

### **Smart Automation Rules:**
```
Dry Soil → Auto Sprinkler (< 35% moisture)
Hot Weather → Auto Fan (> 32°C)
Low Humidity + Heat → Auto Mist (< 55% + > 28°C)
```

## 🚨 **Troubleshooting:**

### **Error: "Collection not found"**
- Pastikan collections sudah dibuat terlebih dahulu
- Import collections schema dulu, baru import data

### **Error: "Validation failed"**
- Check deviceId sesuai dengan devices collection
- Verify JSON format sudah benar

### **Error: "Duplicate data"**
- Data mungkin sudah ada sebelumnya
- Hapus data existing atau skip yang duplicate

### **Data tidak muncul di Flutter app**
- Restart Flutter app
- Check API authentication
- Verify collection names dan field names

## 🎉 **Success Indicators:**

✅ **All sample data imported successfully**
✅ **API endpoints returning data**
✅ **Flutter app displaying sample data**
✅ **Schedule modal working with real data**
✅ **Sensor dashboard showing readings**
✅ **Automation rules visible**

---

**📱 Ready for Testing**: Setelah import selesai, aplikasi Flutter siap untuk testing dengan data yang realistis!
