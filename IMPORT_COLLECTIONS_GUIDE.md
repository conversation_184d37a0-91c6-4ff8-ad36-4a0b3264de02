# 📋 Panduan Import Collections PocketBase - Step by Step

## 🎯 **Mengapa Dibuat Terpisah?**

- ✅ **Men<PERSON><PERSON><PERSON> error validasi** yang kompleks
- ✅ **Import satu per satu** lebih aman
- ✅ **Debugging lebih mudah** jika ada masalah
- ✅ **Rollback individual** jika dip<PERSON>an

## 📁 **File Collections yang Tersedia:**

1. **`pocketbase_schedules.json`** - Collection untuk jadwal otomatis
2. **`pocketbase_automation_rules.json`** - Collection untuk aturan otomasi
3. **`pocketbase_schedule_executions.json`** - Collection untuk log eksekusi

## 🚀 **Cara Import (Step-by-Step):**

### **Step 1: Import Schedules Collection**

1. Buka PocketBase Admin: `http://**************:8080/_/`
2. Login sebagai admin
3. Klik **Settings** → **Import collections**
4. Copy-paste isi file `pocketbase_schedules.json`
5. Klik **Review** untuk preview
6. Klik **Import**
7. ✅ **Verify**: Collection `schedules` terbuat

### **Step 2: Import Automation Rules Collection**

1. Masih di **Settings** → **Import collections**
2. Copy-paste isi file `pocketbase_automation_rules.json`
3. Klik **Review** untuk preview
4. Klik **Import**
5. ✅ **Verify**: Collection `automation_rules` terbuat

### **Step 3: Import Schedule Executions Collection**

1. Masih di **Settings** → **Import collections**
2. Copy-paste isi file `pocketbase_schedule_executions.json`
3. Klik **Review** untuk preview
4. Klik **Import**
5. ✅ **Verify**: Collection `schedule_executions` terbuat

## ✅ **Verification Checklist:**

Setelah import selesai, pastikan:

### **Collections Created:**
- [ ] `schedules` - dengan 11 fields (name, description, deviceId, etc.)
- [ ] `automation_rules` - dengan 8 fields (name, description, conditions, etc.)
- [ ] `schedule_executions` - dengan 5 fields (scheduleId, executedAt, status, etc.)

### **API Endpoints Available:**
- [ ] `GET /api/collections/schedules/records`
- [ ] `POST /api/collections/schedules/records`
- [ ] `GET /api/collections/automation_rules/records`
- [ ] `POST /api/collections/automation_rules/records`
- [ ] `GET /api/collections/schedule_executions/records`
- [ ] `POST /api/collections/schedule_executions/records`

### **Test API dengan curl:**
```bash
# Test schedules endpoint
curl "http://**************:8080/api/collections/schedules/records"

# Test automation rules endpoint
curl "http://**************:8080/api/collections/automation_rules/records"

# Test schedule executions endpoint
curl "http://**************:8080/api/collections/schedule_executions/records"
```

## 🔧 **Schema Details:**

### **Schedules Collection:**
```
Fields:
- name (text, required) - Schedule name
- description (text, optional) - Schedule description
- deviceId (text, required) - Device ID reference
- controlId (text, required) - Control ID reference
- type (select, required) - time_based, sensor_based, manual
- startTime (date, required) - Start time
- endTime (date, optional) - End time
- daysOfWeek (json, optional) - Array of days [0-6]
- isActive (bool, optional) - Active status
- isRepeating (bool, optional) - Repeating schedule
- parameters (json, optional) - Additional parameters

API Rules: @request.auth.id != ""
```

### **Automation Rules Collection:**
```
Fields:
- name (text, required) - Rule name
- description (text, optional) - Rule description
- deviceId (text, required) - Device ID reference
- controlId (text, required) - Control ID reference
- conditions (json, required) - Array of conditions
- actions (json, required) - Array of actions
- isActive (bool, optional) - Active status
- logic (select, required) - AND, OR

API Rules: @request.auth.id != ""
```

### **Schedule Executions Collection:**
```
Fields:
- scheduleId (text, required) - Schedule ID reference
- executedAt (date, required) - Execution timestamp
- status (select, required) - success, failed, skipped
- result (text, optional) - Execution result
- error (text, optional) - Error message if failed

API Rules: @request.auth.id != ""
```

## 🚨 **Troubleshooting:**

### **Error: "Collection already exists"**
- Skip yang sudah ada, lanjut ke collection berikutnya
- Atau hapus collection yang conflict terlebih dahulu

### **Error: "Field validation failed"**
- Check format JSON sudah benar
- Pastikan tidak ada trailing comma
- Verify field IDs unique dan panjang 5+ karakter

### **Error: "Authentication required"**
- Pastikan sudah login sebagai admin
- Check API rules sudah diset dengan benar

### **Import berhasil tapi API error**
- Restart PocketBase server
- Check collection names dan field names
- Verify API endpoints dengan browser

## 📱 **Test dengan Flutter App:**

Setelah import selesai:

1. **Restart Flutter app**
2. **Navigate ke Scheduling screen**
3. **Tap quick schedule card**
4. **Schedule modal harus terbuka**
5. **Fill form dan create schedule**
6. **Verify schedule muncul di list**

## 🎯 **Sample Data untuk Testing:**

Setelah collections terbuat, Anda bisa menambah sample data:

### **Sample Schedule:**
```json
{
  "name": "Penyiraman Pagi",
  "description": "Penyiraman otomatis setiap pagi",
  "deviceId": "016j4islabono91",
  "controlId": "sprinkler",
  "type": "time_based",
  "startTime": "2024-01-15T06:00:00.000Z",
  "endTime": "2024-01-15T06:30:00.000Z",
  "daysOfWeek": [1, 2, 3, 4, 5],
  "isActive": true,
  "isRepeating": true,
  "parameters": {
    "duration": 30,
    "intensity": "medium"
  }
}
```

### **Sample Automation Rule:**
```json
{
  "name": "Auto Sprinkler - Tanah Kering",
  "description": "Nyalakan sprinkler jika kelembaban tanah < 35%",
  "deviceId": "016j4islabono91",
  "controlId": "sprinkler",
  "conditions": [
    {
      "sensor": "soilMoisture",
      "operator": "<",
      "value": 35,
      "unit": "percent"
    }
  ],
  "actions": [
    {
      "type": "turn_on",
      "controlId": "sprinkler",
      "parameters": {
        "duration": 20,
        "intensity": "medium"
      }
    }
  ],
  "isActive": true,
  "logic": "AND"
}
```

## 🎉 **Success Indicators:**

✅ **Collections imported successfully**
✅ **API endpoints responding**
✅ **Flutter app can create schedules**
✅ **Schedule modal working**
✅ **No validation errors**

---

**📞 Support**: Jika masih ada error, coba import satu per satu dan check error message untuk debugging yang lebih spesifik.
